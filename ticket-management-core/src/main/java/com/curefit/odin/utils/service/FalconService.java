package com.curefit.odin.utils.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.falcon.Filter;
import com.curefit.falcon.FilterType;
import com.curefit.falcon.Location;
import com.curefit.falcon.SelectFilterValue;
import com.curefit.falcon.client.SearchClient;
import com.curefit.falcon.request.SearchRequest;
import com.curefit.falcon.response.SearchResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
public class FalconService {

    @Autowired
    SearchClient searchClient;

    @Autowired
    CommonHttpHelper commonHttpHelper;

    @Value("${services.falcon.apiKey}")
    private String falconApiKey;

    @Value("${falcon.etl.url}")
    private String falconEtlUrl;

    @Cacheable(value = "fetchUserByEmailIdV2", unless = "#result == null")
    public SearchResponse fetchUserByEmailId(String emailId) throws BaseException {
        try {
            return searchClient.search(getSearchRequest(emailId), "odin", UUID.randomUUID().toString(), null, null, false);
        } catch (Exception e) {
            log.error("Unexpected error while fetching user details for {} : {}", emailId, e.getMessage(), e);
            throw new RuntimeException("Unexpected error communicating with Falcon", e);
        }
    }

    public SearchRequest getSearchRequest(String query) {
        SearchRequest searchRequest = new SearchRequest(1, 20);
        searchRequest.setQuery(query);
        searchRequest.setLocations(Collections.singletonList(new Location("IN", "Bangalore")));
        searchRequest.setAppliedFilters(getAppliedFilters());
        return searchRequest;
    }

    public List<Filter> getAppliedFilters() {
        // Creating applied filters with nested structure
        SelectFilterValue userValue = new SelectFilterValue();
        userValue.setTitle("User");
        userValue.setValue("User");
        Filter entityFilter = new Filter(null, "attributes.entityDisplayName", "Entity", FilterType.SELECT, null, null, Collections.singletonList(userValue), null, null);
        SelectFilterValue ticketingSystemValue = new SelectFilterValue(null, "TICKETING_SYSTEM", null, null, Collections.singletonList(entityFilter));
        ticketingSystemValue.setTitle("TICKETING_SYSTEM");
        Filter subVerticalFilter = new Filter(null, "subVertical", "SubVertical", FilterType.SELECT, null, null, Collections.singletonList(ticketingSystemValue), null, null);
        SelectFilterValue odinValue = new SelectFilterValue(null, "ODIN", null, null, Collections.singletonList(subVerticalFilter));
        odinValue.setTitle("ODIN");
        Filter verticalFilter = new Filter(null, "vertical", "Vertical", FilterType.SELECT, null, null, Collections.singletonList(odinValue), null, null);
        return Collections.singletonList(verticalFilter);
    }

    public void refreshUsers() {
        try {
            String url = falconEtlUrl + "?vertical=ODIN&subVertical=TICKETING_SYSTEM&entityType=USER";
            Map<String, String> headers = new HashMap<>();
            headers.put("accept", "*/*");
            headers.put("Content-Type", "application/json");
            headers.put("apiKey", falconApiKey);

            log.info("Triggering Falcon ETL refresh for users");
            ResponseEntity<Object> response = commonHttpHelper.request(url, HttpMethod.POST, null, headers, Object.class);

            if (response != null && response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully triggered Falcon ETL refresh for users. Response: {}", response.getBody());
            } else {
                log.warn("Falcon ETL refresh returned non-success status: {}", response != null ? response.getStatusCode() : "null response");
            }
        } catch (Exception e) {
            log.error("Error while refreshing users in Falcon: {}", e.getMessage(), e);
        }
    }
}
