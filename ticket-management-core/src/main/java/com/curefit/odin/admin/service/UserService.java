package com.curefit.odin.admin.service;

import com.curefit.cf.commons.pojo.auth.MembershipEntry;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.auth.IdentityHelper;
import com.curefit.commons.sf.auth.MembershipHelper;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.HeadersUtils;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.falcon.response.SearchResponse;
import com.curefit.odin.admin.models.User;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.repositories.UserDAO;
import com.curefit.odin.config.OdinConfigurations;
import com.curefit.odin.utils.pojo.MaverickUserEntry;
import com.curefit.odin.utils.service.FalconService;
import com.curefit.odin.utils.service.MaverickService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.curefit.odin.commons.Constants.*;

@Service
@Slf4j
public class UserService extends BaseMySQLService<User, UserEntry> {


    @Autowired
    MaverickService maverickService;

    @Autowired
    RollbarService rollbarService;

    @Autowired
    IdentityHelper identityHelper;

    @Autowired
    MembershipHelper membershipHelper;

    @Autowired
    OdinConfigurations odinConfigurations;

    @Autowired
    FalconService falconService;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    AppConfigCache appConfigCache;

    public UserService(UserDAO crudRepository) {
        super(crudRepository);
    }

    public void addUserAndRefreshMaverick(UserEntry entry) throws BaseException {
        if(isFalconUserSearchSupported()){
            Optional<User> optionalUser = ((UserDAO) baseMySQLRepository).findByEmailId(entry.getEmailId());
            if (optionalUser.isPresent()) return;
            entry.setMemberCount(1);
            entry.setIsDl(false);
            entry.setActive(true);
            super.create(entry);
            falconService.refreshUsers();
            return;
        }
        if(maverickService.searchUser(entry.getEmailId()).isPresent()) return;
        Optional<User> optionalUser = ((UserDAO) baseMySQLRepository).findByEmailId(entry.getEmailId());
        if (optionalUser.isPresent()) return;
        entry.setMemberCount(1);
        entry.setIsDl(false);
        entry.setActive(true);
        super.create(entry);
        maverickService.refreshUsers();
    }

    public UserEntry createExternalUserIfNotExist(UserEntry entry) throws BaseException {
        Optional<User> optionalUser = ((UserDAO) baseMySQLRepository).findByEmailId(entry.getEmailId());
        if (optionalUser.isPresent()) {
            entry = convertToEntry(optionalUser.get());
        } else {
            entry = super.create(entry);
        }
        return entry;
    }

    public List<UserEntry> addExternalUsers(List<UserEntry> entries) {
        return entries.stream().map(entry -> {
            try {
                entry.setMemberCount(0);
                entry.setIsDl(false);
                entry.setActive(true);
                entry = createExternalUserIfNotExist(entry);
                addMembership(entry.getEmailId());
            } catch (Exception e) {
                log.error("exception in adding users", e);
                rollbarService.error(e);
            }
            return entry;
        }).collect(Collectors.toList());
    }

    private void addMembership(String email) throws BaseException {
        Long identityId;
        try {
            identityId = identityHelper.fetchIdentity(TENANT, email, null).getId();
        } catch (RestClientException e) {
            throw new BaseException("User " + email + " is not present in Identity", AppStatus.BAD_REQUEST, LogType.WARNING);
        }

        membershipHelper.addMembership(odinConfigurations.getWatchmenApiKey(), MembershipEntry.builder()
                .context(WATCHMEN_CONTEXT)
                .identityUserId(identityId)
                .role(WATCHMEN_ROLE)
                .namespace(AUTH_NAMESPACE)
                .modifiedBy(HeadersUtils.getCurrentUser())
                .build());
    }


    public UserEntry findUserByMailId(String emailId) throws BaseException {
        log.debug("Fetching user by mail id {}", emailId);
        if (StringUtils.isBlank(emailId)) {
            throw new BaseException(AppStatus.BAD_REQUEST);
        }
        if (emailId.equalsIgnoreCase("system")) {
            return new UserEntry("System User", emailId, false, 1);
        }
        if(BooleanUtils.isTrue(isFalconUserSearchSupported())){
            try {
                SearchResponse searchResponse = falconService.fetchUserByEmailId(emailId);
                log.info("Search response for user {} : {}", emailId, searchResponse);
                if (CollectionUtils.isEmpty(searchResponse.getResults())) {
                    log.error("User - {} Not found", emailId);
                    return new UserEntry(DISABLED_USER, emailId, false, 1);
                }
                return objectMapper.convertValue(searchResponse.getResults().getFirst().getAttributes(), UserEntry.class);
            } catch (BaseException e) {
                log.error("Error while finding user with emailId {} : {}", emailId, e.getMessage(), e);
                throw e;
            }
        }else{
            try {
                MaverickUserEntry maverickUser = maverickService.fetchUserByEmailId(emailId);
                return new UserEntry(maverickUser.getFullName(), maverickUser.getEmail(), maverickUser.getMobileNumber(), maverickUser.getIsDl(), maverickUser.getMemberCount());
            } catch (BaseException e) {
                if (e.getAppStatus() == AppStatus.BAD_REQUEST) {
                    log.error("User - {} Not found in maverick", emailId);
                    return new UserEntry(DISABLED_USER, emailId, false, 1);
                }
                throw e;
            }
        }
    }

    public List<UserEntry> findUsersByQuery(String query) throws BaseException {
        if (StringUtils.isBlank(query)) {
            return new ArrayList<>();
        }
        try {
            SearchResponse searchResponse = falconService.fetchUserByEmailId(query);
            if (CollectionUtils.isEmpty(searchResponse.getResults())) {
                return new ArrayList<>();
            }
            return searchResponse.getResults().stream()
                    .map(baseIndexEntry -> objectMapper.convertValue(baseIndexEntry.getAttributes(), UserEntry.class)).collect(Collectors.toList());
        } catch (BaseException e) {
            log.error("Error while fetching user from query {} : {}", query, e.getMessage(), e);
            throw e;
        }
    }

    public List<UserEntry> fetchAll() {
        log.info("fetching all users");
        Iterable<User> users = baseMySQLRepository.findAll();
        return StreamSupport.stream(users.spliterator(), false).map(this::convertToEntry).collect(Collectors.toList());
    }

    public String getUserNameFromMailId(String emailId) {
        String username;
        try {
            UserEntry user = findUserByMailId(emailId);
            username = user.getName();
        } catch (Exception e) {
            username = emailId;
        }
        return username;
    }

    public UserEntry findUserByMailIdWithDefault(String emailId) {
        log.debug("Fetching user by mail id {}", emailId);
        try {
            return findUserByMailId(emailId);
        } catch (BaseException e) {
            log.error(e.getMessage(), e);
            return new UserEntry(emailId, emailId);
        }
    }

    public void createOrUpdate(String dl, int memberCount) {
        try {
            Optional<User> optionalUser = ((UserDAO) baseMySQLRepository).findByEmailId(dl);
            UserEntry userEntry = new UserEntry();
            userEntry.setMemberCount(memberCount);
            if (optionalUser.isPresent()) {
                User user = optionalUser.get();
                if (user.getMemberCount() != memberCount) {
                    patchUpdate(user.getId(), userEntry);
                }
            } else {
                userEntry.setName(dl);
                userEntry.setEmailId(dl);
                userEntry.setIsDl(true);
                create(userEntry);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
    }

    public List<String> filterExistingUserMailIds(List<String> emailIds) {
        return emailIds.stream()
            .filter(emailId -> !StringUtils.isBlank(emailId))
            .filter(emailId -> !findUserByMailIdWithDefault(emailId).getName().equalsIgnoreCase(DISABLED_USER))
            .collect(Collectors.toList());
    }

    public Boolean isFalconUserSearchSupported() {
        try{
            return appConfigCache.getConfig("FALCON_FEATURE_FLAG", new TypeReference<>() {
            }, false);
        } catch (Exception e) {
            log.error("Error fetching Falcon feature flag: {}", e.getMessage(), e);
            return false;
        }
    }
}
